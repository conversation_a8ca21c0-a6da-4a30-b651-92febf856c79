"""
Phishing domains detection routes.
"""
import logging
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Query, Depends

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse
from app.models.phishing_domains_request import PhishingDomainsRequest
from app.tasks.cti.phishing_domains import run_phishing_domain_scan

from app.utils.response_utils import create_task_response


logger = logging.getLogger(__name__)

router = APIRouter(prefix="/phishing_domains")

@router.post("", response_model=ExecutionResponse)
async def scan_domain_for_phishing(request: PhishingDomainsRequest) -> ExecutionResponse:
    """
    Start phishing domains scan for a target domain.

    Request Body:
    {
        "domain": "example.com"
    }
    """
    try:
        # Extract domain from request
        domain = request.domain
        # If job_id is provided, check if operation exists

        # Start a new task
        logger.info(f"Starting new phishing domain scan for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="phishing_domains",
            parameters={"domain": domain}
        )

        # Start the task - pass the job_id to the Celery task
        task = run_phishing_domain_scan.delay(domain, job_id)
        logger.info(f"Celery task started with task_id {task.id}")

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            success=True,
            feature="phishing_domains",
            created_at=datetime.now()
        )
    except Exception as e:
        logger.exception(f"Error in phishing domains scan: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        ) 